/**
 * 服务器与云端节点注册表测试
 * 批次6 - 服务器与云端节点注册测试
 */
import { ServerCloudNodesRegistry, SERVER_CLOUD_NODES_STATS } from '../ServerCloudNodesRegistry';
import { NodeRegistry } from '../NodeRegistry';

describe('ServerCloudNodesRegistry', () => {
  let registry: ServerCloudNodesRegistry;

  beforeEach(() => {
    // 重置注册表状态
    registry = ServerCloudNodesRegistry.getInstance();
    registry.reset();
    
    // 清理NodeRegistry
    NodeRegistry.clear();
  });

  afterEach(() => {
    // 清理
    NodeRegistry.clear();
  });

  describe('单例模式', () => {
    it('应该返回同一个实例', () => {
      const instance1 = ServerCloudNodesRegistry.getInstance();
      const instance2 = ServerCloudNodesRegistry.getInstance();
      expect(instance1).toBe(instance2);
    });
  });

  describe('节点注册', () => {
    it('应该成功注册所有58个服务器与云端节点', () => {
      const result = registry.registerAll();
      
      expect(result).toBe(true);
      expect(registry.isRegistered()).toBe(true);
      expect(registry.getRegisteredNodeCount()).toBe(58);
    });

    it('应该防止重复注册', () => {
      // 第一次注册
      const result1 = registry.registerAll();
      expect(result1).toBe(true);
      
      // 第二次注册应该返回true但不重复注册
      const result2 = registry.registerAll();
      expect(result2).toBe(true);
      expect(registry.isRegistered()).toBe(true);
    });

    it('应该正确统计各类别节点数量', () => {
      registry.registerAll();
      
      const stats = registry.getNodeCategoryStats();
      expect(stats['FILE_SERVICE']).toBe(10);
      expect(stats['AUTHENTICATION']).toBe(7);
      expect(stats['NOTIFICATION']).toBe(8);
      expect(stats['MONITORING']).toBe(5);
      expect(stats['PROJECT_MANAGEMENT']).toBe(10);
      expect(stats['EDGE_COMPUTING']).toBe(18);
    });
  });

  describe('节点分类验证', () => {
    beforeEach(() => {
      registry.registerAll();
    });

    it('应该注册正确数量的文件服务节点', () => {
      const fileServiceNodes = NodeRegistry.getNodesByCategory('FILE_SERVICE');
      expect(fileServiceNodes.length).toBe(SERVER_CLOUD_NODES_STATS.FILE_SERVICE_COUNT);
    });

    it('应该注册正确数量的认证授权节点', () => {
      const authNodes = NodeRegistry.getNodesByCategory('AUTHENTICATION');
      expect(authNodes.length).toBe(SERVER_CLOUD_NODES_STATS.AUTHENTICATION_COUNT);
    });

    it('应该注册正确数量的通知服务节点', () => {
      const notificationNodes = NodeRegistry.getNodesByCategory('NOTIFICATION');
      expect(notificationNodes.length).toBe(SERVER_CLOUD_NODES_STATS.NOTIFICATION_COUNT);
    });

    it('应该注册正确数量的监控服务节点', () => {
      const monitoringNodes = NodeRegistry.getNodesByCategory('MONITORING');
      expect(monitoringNodes.length).toBe(SERVER_CLOUD_NODES_STATS.MONITORING_COUNT);
    });

    it('应该注册正确数量的项目管理节点', () => {
      const projectNodes = NodeRegistry.getNodesByCategory('PROJECT_MANAGEMENT');
      expect(projectNodes.length).toBe(SERVER_CLOUD_NODES_STATS.PROJECT_MANAGEMENT_COUNT);
    });

    it('应该注册正确数量的边缘设备节点', () => {
      const edgeNodes = NodeRegistry.getNodesByCategory('EDGE_COMPUTING');
      expect(edgeNodes.length).toBe(SERVER_CLOUD_NODES_STATS.EDGE_DEVICE_COUNT);
    });
  });

  describe('节点功能验证', () => {
    beforeEach(() => {
      registry.registerAll();
    });

    it('应该能够创建文件服务节点实例', () => {
      const fileUploadNode = NodeRegistry.createNode('FileUpload');
      expect(fileUploadNode).toBeDefined();
      expect(fileUploadNode.getType()).toBe('FileUpload');
    });

    it('应该能够创建认证授权节点实例', () => {
      const jwtNode = NodeRegistry.createNode('JWTToken');
      expect(jwtNode).toBeDefined();
      expect(jwtNode.getType()).toBe('JWTToken');
    });

    it('应该能够创建通知服务节点实例', () => {
      const emailNode = NodeRegistry.createNode('EmailNotification');
      expect(emailNode).toBeDefined();
      expect(emailNode.getType()).toBe('EmailNotification');
    });

    it('应该能够创建监控服务节点实例', () => {
      const monitoringNode = NodeRegistry.createNode('SystemMonitoring');
      expect(monitoringNode).toBeDefined();
      expect(monitoringNode.getType()).toBe('SystemMonitoring');
    });

    it('应该能够创建项目管理节点实例', () => {
      const projectNode = NodeRegistry.createNode('CreateProject');
      expect(projectNode).toBeDefined();
      expect(projectNode.getType()).toBe('CreateProject');
    });

    it('应该能够创建边缘设备节点实例', () => {
      const edgeNode = NodeRegistry.createNode('EdgeDeviceRegistration');
      expect(edgeNode).toBeDefined();
      expect(edgeNode.getType()).toBe('EdgeDeviceRegistration');
    });
  });

  describe('错误处理', () => {
    it('应该处理注册过程中的错误', () => {
      // 模拟注册错误
      const originalRegisterNode = NodeRegistry.registerNode;
      NodeRegistry.registerNode = jest.fn().mockImplementation(() => {
        throw new Error('注册失败');
      });

      const result = registry.registerAll();
      expect(result).toBe(false);
      expect(registry.isRegistered()).toBe(false);

      // 恢复原始方法
      NodeRegistry.registerNode = originalRegisterNode;
    });
  });

  describe('重置功能', () => {
    it('应该能够重置注册状态', () => {
      registry.registerAll();
      expect(registry.isRegistered()).toBe(true);

      registry.reset();
      expect(registry.isRegistered()).toBe(false);
    });
  });

  describe('统计信息', () => {
    it('应该返回正确的节点统计信息', () => {
      expect(SERVER_CLOUD_NODES_STATS.TOTAL_NODES).toBe(58);
      expect(SERVER_CLOUD_NODES_STATS.FILE_SERVICE_COUNT).toBe(10);
      expect(SERVER_CLOUD_NODES_STATS.AUTHENTICATION_COUNT).toBe(7);
      expect(SERVER_CLOUD_NODES_STATS.NOTIFICATION_COUNT).toBe(8);
      expect(SERVER_CLOUD_NODES_STATS.MONITORING_COUNT).toBe(5);
      expect(SERVER_CLOUD_NODES_STATS.PROJECT_MANAGEMENT_COUNT).toBe(10);
      expect(SERVER_CLOUD_NODES_STATS.EDGE_DEVICE_COUNT).toBe(18);
    });

    it('各类别节点数量总和应该等于总节点数', () => {
      const sum = SERVER_CLOUD_NODES_STATS.FILE_SERVICE_COUNT +
                  SERVER_CLOUD_NODES_STATS.AUTHENTICATION_COUNT +
                  SERVER_CLOUD_NODES_STATS.NOTIFICATION_COUNT +
                  SERVER_CLOUD_NODES_STATS.MONITORING_COUNT +
                  SERVER_CLOUD_NODES_STATS.PROJECT_MANAGEMENT_COUNT +
                  SERVER_CLOUD_NODES_STATS.EDGE_DEVICE_COUNT;
      
      expect(sum).toBe(SERVER_CLOUD_NODES_STATS.TOTAL_NODES);
    });
  });
});
