/**
 * 边缘计算与5G节点注册表
 * 注册批次7：边缘计算与5G（59个节点）
 * 包括边缘设备管理、边缘AI、云边协调、边缘路由、5G网络、计算机视觉和自然语言处理节点
 */

import { NodeRegistry } from '../nodes/NodeRegistry';
import { Debug } from '../../utils/Debug';

// 导入边缘设备管理节点（剩余7个）
import {
  EdgeConfigurationNode,
  EdgeMaintenanceNode,
  EdgeBackupNode
} from '../nodes/edge/EdgeDeviceNodes5';

import {
  EdgeSyncNode,
  EdgeAnalyticsNode
} from '../nodes/edge/EdgeDeviceNodes6';

import {
  EdgeModelCacheNode,
  EdgeModelVersioningNode
} from '../nodes/edge/EdgeAINodes4';

// 导入边缘AI节点（12个）
import {
  EdgeAIInferenceNode,
  EdgeModelDeploymentNode,
  EdgeModelOptimizationNode
} from '../nodes/edge/EdgeAINodes';

import {
  EdgeFederatedLearningNode,
  EdgeAIMonitoringNode
} from '../nodes/edge/EdgeAINodes2';

import {
  EdgeAIPerformanceNode,
  EdgeAISecurityNode,
  EdgeAIAnalyticsNode
} from '../nodes/edge/EdgeAINodes3';

import {
  EdgeAIResourceNode,
  EdgeAISchedulerNode,
  EdgeModelUpdateNode
} from '../nodes/edge/EdgeAINodes5';

// 导入云边协调节点（8个）
import {
  CloudEdgeOrchestrationNode,
  HybridComputingNode
} from '../nodes/edge/CloudEdgeNodes';

import {
  DataSynchronizationNode,
  TaskDistributionNode
} from '../nodes/edge/CloudEdgeNodes2';

import {
  ResourceOptimizationNode,
  LatencyOptimizationNode
} from '../nodes/edge/CloudEdgeNodes3';

import {
  BandwidthOptimizationNode,
  CostOptimizationNode
} from '../nodes/edge/CloudEdgeNodes4';

// 导入边缘路由节点（6个）
import {
  EdgeRoutingNode,
  EdgeLoadBalancingNode,
  EdgeCachingNode,
  EdgeCompressionNode
} from '../nodes/edge/EdgeRoutingNodes';

import {
  EdgeOptimizationNode,
  EdgeQoSNode
} from '../nodes/edge/EdgeRoutingNodes2';

// 导入5G网络节点（8个）
import {
  FiveGConnectionNode,
  FiveGSlicingNode,
  FiveGQoSNode
} from '../nodes/edge/FiveGNetworkNodes';

import {
  FiveGLatencyNode,
  FiveGBandwidthNode
} from '../nodes/edge/FiveGNetworkNodes2';

import {
  FiveGSecurityNode,
  FiveGMonitoringNode,
  FiveGOptimizationNode
} from '../nodes/edge/FiveGNetworkNodes3';

// 导入计算机视觉节点（15个）
import {
  ImageSegmentationNode,
  ObjectTrackingNode,
  FaceRecognitionNode,
  OpticalCharacterRecognitionNode
} from '../nodes/ai/ComputerVisionNodes3';

import {
  ImageGenerationNode,
  StyleTransferNode,
  ImageEnhancementNode,
  AugmentedRealityNode
} from '../nodes/ai/ComputerVisionNodes4';

import {
  MotionTrackingNode,
  DepthEstimationNode,
  PoseEstimationNode,
  SceneUnderstandingNode,
  VisualSLAMNode,
  StereoVisionNode,
  PointCloudProcessingNode
} from '../nodes/ai/ComputerVisionNodes5';

// 导入自然语言处理节点（3个）
import {
  MachineTranslationNode,
  QuestionAnsweringNode,
  TextGenerationNode
} from '../nodes/ai/NaturalLanguageProcessingNodes2';

/**
 * 边缘计算与5G节点注册表类
 */
export class EdgeComputing5GNodesRegistry {
  private static instance: EdgeComputing5GNodesRegistry;
  private nodeRegistry: NodeRegistry;
  private registeredNodes: Map<string, any> = new Map();
  private registrationErrors: string[] = [];

  private constructor() {
    this.nodeRegistry = NodeRegistry.getInstance();
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): EdgeComputing5GNodesRegistry {
    if (!EdgeComputing5GNodesRegistry.instance) {
      EdgeComputing5GNodesRegistry.instance = new EdgeComputing5GNodesRegistry();
    }
    return EdgeComputing5GNodesRegistry.instance;
  }

  /**
   * 注册所有边缘计算与5G节点
   */
  public registerAllNodes(): void {
    console.log('开始注册批次7：边缘计算与5G节点...');
    
    try {
      this.registerEdgeDeviceManagementNodes();
      this.registerEdgeAINodes();
      this.registerCloudEdgeCoordinationNodes();
      this.registerEdgeRoutingNodes();
      this.registerFiveGNetworkNodes();
      this.registerComputerVisionNodes();
      this.registerNaturalLanguageProcessingNodes();
      
      console.log(`✅ 批次7节点注册完成，总计: ${this.registeredNodes.size}个节点`);
      
      // 输出注册统计
      this.printRegistrationStatistics();
      
    } catch (error) {
      const errorMsg = `批次7节点注册失败: ${error}`;
      this.registrationErrors.push(errorMsg);
      Debug.error('EdgeComputing5GNodesRegistry', errorMsg);
      throw error;
    }
  }

  /**
   * 注册边缘设备管理节点（剩余7个）
   */
  private registerEdgeDeviceManagementNodes(): void {
    const edgeDeviceNodes = [
      EdgeConfigurationNode,
      EdgeMaintenanceNode,
      EdgeBackupNode,
      EdgeSyncNode,
      EdgeAnalyticsNode,
      EdgeModelCacheNode,
      EdgeModelVersioningNode
    ];

    const category = 'Edge/DeviceManagement';
    
    for (const NodeClass of edgeDeviceNodes) {
      this.nodeRegistry.registerNode(NodeClass, category);
      this.registeredNodes.set(NodeClass.TYPE, NodeClass);
    }

    console.log(`✅ 已注册 ${edgeDeviceNodes.length} 个边缘设备管理节点`);
  }

  /**
   * 注册边缘AI节点（12个）
   */
  private registerEdgeAINodes(): void {
    const edgeAINodes = [
      EdgeAIInferenceNode,
      EdgeModelDeploymentNode,
      EdgeModelOptimizationNode,
      EdgeFederatedLearningNode,
      EdgeAIMonitoringNode,
      EdgeAIPerformanceNode,
      EdgeAISecurityNode,
      EdgeAIAnalyticsNode,
      EdgeAIResourceNode,
      EdgeAISchedulerNode,
      EdgeModelUpdateNode
    ];

    const category = 'Edge/AI';
    
    for (const NodeClass of edgeAINodes) {
      this.nodeRegistry.registerNode(NodeClass, category);
      this.registeredNodes.set(NodeClass.TYPE, NodeClass);
    }

    console.log(`✅ 已注册 ${edgeAINodes.length} 个边缘AI节点`);
  }

  /**
   * 注册云边协调节点（8个）
   */
  private registerCloudEdgeCoordinationNodes(): void {
    const cloudEdgeNodes = [
      CloudEdgeOrchestrationNode,
      HybridComputingNode,
      DataSynchronizationNode,
      TaskDistributionNode,
      ResourceOptimizationNode,
      LatencyOptimizationNode,
      BandwidthOptimizationNode,
      CostOptimizationNode
    ];

    const category = 'Edge/CloudEdge';
    
    for (const NodeClass of cloudEdgeNodes) {
      this.nodeRegistry.registerNode(NodeClass, category);
      this.registeredNodes.set(NodeClass.TYPE, NodeClass);
    }

    console.log(`✅ 已注册 ${cloudEdgeNodes.length} 个云边协调节点`);
  }

  /**
   * 注册边缘路由节点（6个）
   */
  private registerEdgeRoutingNodes(): void {
    const edgeRoutingNodes = [
      EdgeRoutingNode,
      EdgeLoadBalancingNode,
      EdgeCachingNode,
      EdgeCompressionNode,
      EdgeOptimizationNode,
      EdgeQoSNode
    ];

    const category = 'Edge/Routing';
    
    for (const NodeClass of edgeRoutingNodes) {
      this.nodeRegistry.registerNode(NodeClass, category);
      this.registeredNodes.set(NodeClass.TYPE, NodeClass);
    }

    console.log(`✅ 已注册 ${edgeRoutingNodes.length} 个边缘路由节点`);
  }

  /**
   * 注册5G网络节点（8个）
   */
  private registerFiveGNetworkNodes(): void {
    const fiveGNodes = [
      FiveGConnectionNode,
      FiveGSlicingNode,
      FiveGQoSNode,
      FiveGLatencyNode,
      FiveGBandwidthNode,
      FiveGSecurityNode,
      FiveGMonitoringNode,
      FiveGOptimizationNode
    ];

    const category = 'Edge/5G';
    
    for (const NodeClass of fiveGNodes) {
      this.nodeRegistry.registerNode(NodeClass, category);
      this.registeredNodes.set(NodeClass.TYPE, NodeClass);
    }

    console.log(`✅ 已注册 ${fiveGNodes.length} 个5G网络节点`);
  }

  /**
   * 注册计算机视觉节点（15个）
   */
  private registerComputerVisionNodes(): void {
    const computerVisionNodes = [
      ImageSegmentationNode,
      ObjectTrackingNode,
      FaceRecognitionNode,
      OpticalCharacterRecognitionNode,
      ImageGenerationNode,
      StyleTransferNode,
      ImageEnhancementNode,
      AugmentedRealityNode,
      MotionTrackingNode,
      DepthEstimationNode,
      PoseEstimationNode,
      SceneUnderstandingNode,
      VisualSLAMNode,
      StereoVisionNode,
      PointCloudProcessingNode
    ];

    const category = 'AI/ComputerVision';

    for (const NodeClass of computerVisionNodes) {
      this.nodeRegistry.registerNode(NodeClass, category);
      this.registeredNodes.set(NodeClass.TYPE, NodeClass);
    }

    console.log(`✅ 已注册 ${computerVisionNodes.length} 个计算机视觉节点`);
  }

  /**
   * 注册自然语言处理节点（3个）
   */
  private registerNaturalLanguageProcessingNodes(): void {
    const nlpNodes = [
      MachineTranslationNode,
      QuestionAnsweringNode,
      TextGenerationNode
    ];

    const category = 'AI/NLP';

    for (const NodeClass of nlpNodes) {
      this.nodeRegistry.registerNode(NodeClass, category);
      this.registeredNodes.set(NodeClass.TYPE, NodeClass);
    }

    console.log(`✅ 已注册 ${nlpNodes.length} 个自然语言处理节点`);
  }

  /**
   * 输出注册统计信息
   */
  private printRegistrationStatistics(): void {
    console.log('\n📊 批次7节点注册统计:');
    console.log('├─ 边缘设备管理节点: 7个');
    console.log('├─ 边缘AI节点: 12个');
    console.log('├─ 云边协调节点: 8个');
    console.log('├─ 边缘路由节点: 6个');
    console.log('├─ 5G网络节点: 8个');
    console.log('├─ 计算机视觉节点: 15个');
    console.log('├─ 自然语言处理节点: 3个');
    console.log(`└─ 总计: ${this.registeredNodes.size}个节点\n`);
  }

  /**
   * 获取已注册的节点数量
   */
  public getRegisteredNodeCount(): number {
    return this.registeredNodes.size;
  }

  /**
   * 获取注册错误列表
   */
  public getRegistrationErrors(): string[] {
    return [...this.registrationErrors];
  }

  /**
   * 检查节点是否已注册
   */
  public isNodeRegistered(nodeType: string): boolean {
    return this.registeredNodes.has(nodeType);
  }

  /**
   * 获取所有已注册的节点类型
   */
  public getAllRegisteredNodeTypes(): string[] {
    return Array.from(this.registeredNodes.keys());
  }

  /**
   * 获取节点分类信息
   */
  public getNodeCategories(): Record<string, string[]> {
    return {
      'Edge/DeviceManagement': [
        'EdgeConfigurationNode',
        'EdgeMaintenanceNode',
        'EdgeBackupNode',
        'EdgeSyncNode',
        'EdgeAnalyticsNode',
        'EdgeModelCacheNode',
        'EdgeModelVersioningNode'
      ],
      'Edge/AI': [
        'EdgeAIInferenceNode',
        'EdgeModelDeploymentNode',
        'EdgeModelOptimizationNode',
        'EdgeFederatedLearningNode',
        'EdgeAIMonitoringNode',
        'EdgeAIPerformanceNode',
        'EdgeAISecurityNode',
        'EdgeAIAnalyticsNode',
        'EdgeAIResourceNode',
        'EdgeAISchedulerNode',
        'EdgeModelUpdateNode'
      ],
      'Edge/CloudEdge': [
        'CloudEdgeOrchestrationNode',
        'HybridComputingNode',
        'DataSynchronizationNode',
        'TaskDistributionNode',
        'ResourceOptimizationNode',
        'LatencyOptimizationNode',
        'BandwidthOptimizationNode',
        'CostOptimizationNode'
      ],
      'Edge/Routing': [
        'EdgeRoutingNode',
        'EdgeLoadBalancingNode',
        'EdgeCachingNode',
        'EdgeCompressionNode',
        'EdgeOptimizationNode',
        'EdgeQoSNode'
      ],
      'Edge/5G': [
        'FiveGConnectionNode',
        'FiveGSlicingNode',
        'FiveGQoSNode',
        'FiveGLatencyNode',
        'FiveGBandwidthNode',
        'FiveGSecurityNode',
        'FiveGMonitoringNode',
        'FiveGOptimizationNode'
      ],
      'AI/ComputerVision': [
        'ImageSegmentationNode',
        'ObjectTrackingNode',
        'FaceRecognitionNode',
        'OpticalCharacterRecognitionNode',
        'ImageGenerationNode',
        'StyleTransferNode',
        'ImageEnhancementNode',
        'AugmentedRealityNode',
        'MotionTrackingNode',
        'DepthEstimationNode',
        'PoseEstimationNode',
        'SceneUnderstandingNode',
        'VisualSLAMNode',
        'StereoVisionNode',
        'PointCloudProcessingNode'
      ],
      'AI/NLP': [
        'MachineTranslationNode',
        'QuestionAnsweringNode',
        'TextGenerationNode'
      ]
    };
  }
}

// 导出单例实例
export const edgeComputing5GNodesRegistry = EdgeComputing5GNodesRegistry.getInstance();

// 自动注册所有节点
edgeComputing5GNodesRegistry.registerAllNodes();
