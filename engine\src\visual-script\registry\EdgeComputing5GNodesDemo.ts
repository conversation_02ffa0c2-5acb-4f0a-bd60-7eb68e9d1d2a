/**
 * 边缘计算与5G节点注册表演示
 * 展示批次7：边缘计算与5G（59个节点）的注册和使用
 */

import { EdgeComputing5GNodesRegistry } from './EdgeComputing5GNodesRegistry';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { Debug } from '../../utils/Debug';

/**
 * 演示边缘计算与5G节点注册表的功能
 */
export class EdgeComputing5GNodesDemo {
  private registry: EdgeComputing5GNodesRegistry;
  private nodeRegistry: NodeRegistry;

  constructor() {
    this.registry = EdgeComputing5GNodesRegistry.getInstance();
    this.nodeRegistry = NodeRegistry.getInstance();
  }

  /**
   * 运行完整演示
   */
  public async runDemo(): Promise<void> {
    console.log('🚀 开始边缘计算与5G节点注册表演示...\n');

    try {
      // 1. 注册所有节点
      await this.demonstrateNodeRegistration();

      // 2. 展示节点分类
      this.demonstrateNodeCategories();

      // 3. 展示节点查询功能
      this.demonstrateNodeQuery();

      // 4. 展示边缘AI节点使用
      await this.demonstrateEdgeAINodes();

      // 5. 展示5G网络节点使用
      await this.demonstrate5GNetworkNodes();

      // 6. 展示计算机视觉节点使用
      await this.demonstrateComputerVisionNodes();

      // 7. 展示统计信息
      this.demonstrateStatistics();

      console.log('✅ 边缘计算与5G节点注册表演示完成！');

    } catch (error) {
      console.error('❌ 演示过程中发生错误:', error);
    }
  }

  /**
   * 演示节点注册过程
   */
  private async demonstrateNodeRegistration(): Promise<void> {
    console.log('📋 1. 节点注册演示');
    console.log('─'.repeat(50));

    const startTime = Date.now();
    
    // 注册所有节点
    this.registry.registerAllNodes();
    
    const endTime = Date.now();
    const registrationTime = endTime - startTime;

    console.log(`✅ 注册完成，耗时: ${registrationTime}ms`);
    console.log(`📊 注册节点数量: ${this.registry.getRegisteredNodeCount()}`);
    
    const errors = this.registry.getRegistrationErrors();
    if (errors.length > 0) {
      console.log(`⚠️  注册错误: ${errors.length}个`);
      errors.forEach(error => console.log(`   - ${error}`));
    } else {
      console.log('✅ 无注册错误');
    }
    
    console.log();
  }

  /**
   * 演示节点分类功能
   */
  private demonstrateNodeCategories(): void {
    console.log('📂 2. 节点分类演示');
    console.log('─'.repeat(50));

    const categories = this.registry.getNodeCategories();
    
    Object.entries(categories).forEach(([category, nodes]) => {
      console.log(`📁 ${category} (${nodes.length}个节点):`);
      nodes.forEach(node => {
        console.log(`   ├─ ${node}`);
      });
      console.log();
    });
  }

  /**
   * 演示节点查询功能
   */
  private demonstrateNodeQuery(): void {
    console.log('🔍 3. 节点查询演示');
    console.log('─'.repeat(50));

    // 查询特定节点
    const testNodes = [
      'EdgeAIInferenceNode',
      'FiveGConnectionNode',
      'ImageSegmentationNode',
      'MachineTranslationNode',
      'NonExistentNode'
    ];

    testNodes.forEach(nodeType => {
      const isRegistered = this.registry.isNodeRegistered(nodeType);
      const status = isRegistered ? '✅' : '❌';
      console.log(`${status} ${nodeType}: ${isRegistered ? '已注册' : '未注册'}`);
    });

    console.log();
  }

  /**
   * 演示边缘AI节点使用
   */
  private async demonstrateEdgeAINodes(): Promise<void> {
    console.log('🤖 4. 边缘AI节点使用演示');
    console.log('─'.repeat(50));

    // 模拟边缘AI推理
    console.log('🔄 模拟边缘AI推理过程...');
    
    const aiInferenceConfig = {
      deviceId: 'edge-device-001',
      modelId: 'mobilenet-v2',
      inputData: 'sample-image-data',
      batchSize: 1,
      accelerator: 'gpu'
    };

    console.log('📋 推理配置:', JSON.stringify(aiInferenceConfig, null, 2));
    
    // 模拟推理延迟
    await this.sleep(1000);
    
    const inferenceResult = {
      output: 'classification-result',
      confidence: 0.95,
      latency: 15, // ms
      throughput: 67, // fps
      resourceUsage: {
        cpu: 45,
        memory: 60,
        gpu: 80,
        power: 35
      }
    };

    console.log('📊 推理结果:', JSON.stringify(inferenceResult, null, 2));
    console.log();
  }

  /**
   * 演示5G网络节点使用
   */
  private async demonstrate5GNetworkNodes(): Promise<void> {
    console.log('📡 5. 5G网络节点使用演示');
    console.log('─'.repeat(50));

    // 模拟5G连接建立
    console.log('🔄 模拟5G网络连接建立...');
    
    const fiveGConfig = {
      networkSlice: 'eMBB',
      qosProfile: 'ultra-low-latency',
      bandwidth: '1Gbps',
      latency: '1ms'
    };

    console.log('📋 5G配置:', JSON.stringify(fiveGConfig, null, 2));
    
    // 模拟连接建立延迟
    await this.sleep(800);
    
    const connectionResult = {
      status: 'connected',
      signalStrength: -85, // dBm
      throughput: 950, // Mbps
      latency: 1.2, // ms
      packetLoss: 0.001, // %
      jitter: 0.5 // ms
    };

    console.log('📊 连接状态:', JSON.stringify(connectionResult, null, 2));
    console.log();
  }

  /**
   * 演示计算机视觉节点使用
   */
  private async demonstrateComputerVisionNodes(): Promise<void> {
    console.log('👁️ 6. 计算机视觉节点使用演示');
    console.log('─'.repeat(50));

    // 模拟图像分割
    console.log('🔄 模拟图像分割处理...');
    
    const imageSegmentationConfig = {
      inputImage: 'sample-image.jpg',
      modelType: 'semantic-segmentation',
      classes: ['person', 'car', 'building', 'road'],
      threshold: 0.7
    };

    console.log('📋 分割配置:', JSON.stringify(imageSegmentationConfig, null, 2));
    
    // 模拟处理延迟
    await this.sleep(1200);
    
    const segmentationResult = {
      segmentedImage: 'segmented-output.jpg',
      detectedClasses: ['person', 'car', 'road'],
      classConfidences: {
        person: 0.92,
        car: 0.88,
        road: 0.95
      },
      processingTime: 45, // ms
      accuracy: 0.91
    };

    console.log('📊 分割结果:', JSON.stringify(segmentationResult, null, 2));
    console.log();
  }

  /**
   * 演示统计信息
   */
  private demonstrateStatistics(): void {
    console.log('📈 7. 统计信息演示');
    console.log('─'.repeat(50));

    const totalNodes = this.registry.getRegisteredNodeCount();
    const categories = this.registry.getNodeCategories();
    const allNodeTypes = this.registry.getAllRegisteredNodeTypes();

    console.log(`📊 总体统计:`);
    console.log(`   ├─ 总节点数: ${totalNodes}`);
    console.log(`   ├─ 分类数: ${Object.keys(categories).length}`);
    console.log(`   └─ 注册错误: ${this.registry.getRegistrationErrors().length}`);
    console.log();

    console.log(`📋 分类统计:`);
    Object.entries(categories).forEach(([category, nodes]) => {
      const percentage = ((nodes.length / totalNodes) * 100).toFixed(1);
      console.log(`   ├─ ${category}: ${nodes.length}个 (${percentage}%)`);
    });
    console.log();

    console.log(`🏷️  节点类型示例:`);
    allNodeTypes.slice(0, 10).forEach((nodeType, index) => {
      const prefix = index === 9 ? '   └─' : '   ├─';
      console.log(`${prefix} ${nodeType}`);
    });
    if (allNodeTypes.length > 10) {
      console.log(`   └─ ... 还有 ${allNodeTypes.length - 10} 个节点`);
    }
    console.log();
  }

  /**
   * 辅助方法：延迟执行
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

/**
 * 运行演示
 */
export async function runEdgeComputing5GNodesDemo(): Promise<void> {
  const demo = new EdgeComputing5GNodesDemo();
  await demo.runDemo();
}

// 如果直接运行此文件，则执行演示
if (require.main === module) {
  runEdgeComputing5GNodesDemo().catch(console.error);
}
