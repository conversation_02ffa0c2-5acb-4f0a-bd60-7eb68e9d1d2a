/**
 * 服务器与云端节点注册表演示
 * 批次6 - 服务器与云端节点功能演示
 */
import { ServerCloudNodesRegistry, SERVER_CLOUD_NODES_STATS } from '../ServerCloudNodesRegistry';
import { NodeRegistry } from '../NodeRegistry';
import { Debug } from '../../../utils/Debug';

/**
 * 服务器与云端节点演示类
 */
export class ServerCloudNodesDemo {
  private registry: ServerCloudNodesRegistry;

  constructor() {
    this.registry = ServerCloudNodesRegistry.getInstance();
  }

  /**
   * 运行完整演示
   */
  public async runFullDemo(): Promise<void> {
    console.log('🚀 开始服务器与云端节点演示...\n');

    try {
      // 1. 注册节点
      await this.demonstrateNodeRegistration();

      // 2. 演示文件服务节点
      await this.demonstrateFileServiceNodes();

      // 3. 演示认证授权节点
      await this.demonstrateAuthenticationNodes();

      // 4. 演示通知服务节点
      await this.demonstrateNotificationNodes();

      // 5. 演示监控服务节点
      await this.demonstrateMonitoringNodes();

      // 6. 演示项目管理节点
      await this.demonstrateProjectManagementNodes();

      // 7. 演示边缘设备节点
      await this.demonstrateEdgeDeviceNodes();

      // 8. 显示统计信息
      this.showStatistics();

      console.log('✅ 服务器与云端节点演示完成！');
    } catch (error) {
      console.error('❌ 演示过程中发生错误:', error);
    }
  }

  /**
   * 演示节点注册
   */
  private async demonstrateNodeRegistration(): Promise<void> {
    console.log('📋 1. 节点注册演示');
    console.log('==================');

    // 注册所有节点
    const success = this.registry.registerAll();
    console.log(`注册结果: ${success ? '✅ 成功' : '❌ 失败'}`);
    console.log(`注册状态: ${this.registry.isRegistered() ? '已注册' : '未注册'}`);
    console.log(`节点总数: ${this.registry.getRegisteredNodeCount()}`);
    console.log();
  }

  /**
   * 演示文件服务节点
   */
  private async demonstrateFileServiceNodes(): Promise<void> {
    console.log('📁 2. 文件服务节点演示');
    console.log('===================');

    try {
      // 文件上传节点
      const fileUploadNode = NodeRegistry.createNode('FileUpload');
      if (fileUploadNode) {
        console.log('✅ 文件上传节点创建成功');
        const uploadResult = fileUploadNode.execute({
          file: { name: 'test.txt', size: 1024, type: 'text/plain' },
          destination: '/uploads/',
          options: { overwrite: true }
        });
        console.log('   上传结果:', uploadResult.success ? '成功' : '失败');
      }

      // 文件加密节点
      const fileEncryptionNode = NodeRegistry.createNode('FileEncryption');
      if (fileEncryptionNode) {
        console.log('✅ 文件加密节点创建成功');
        const encryptResult = fileEncryptionNode.execute({
          action: 'encrypt',
          fileId: 'test-file-123',
          algorithm: 'AES-256',
          key: 'encryption-key'
        });
        console.log('   加密结果:', encryptResult.success ? '成功' : '失败');
      }

      // 文件搜索节点
      const fileSearchNode = NodeRegistry.createNode('FileSearch');
      if (fileSearchNode) {
        console.log('✅ 文件搜索节点创建成功');
        const searchResult = fileSearchNode.execute({
          query: 'test',
          filters: { type: 'document' },
          limit: 10
        });
        console.log('   搜索结果:', searchResult.success ? `找到${searchResult.totalCount}个文件` : '搜索失败');
      }

    } catch (error) {
      console.error('❌ 文件服务节点演示失败:', error);
    }
    console.log();
  }

  /**
   * 演示认证授权节点
   */
  private async demonstrateAuthenticationNodes(): Promise<void> {
    console.log('🔐 3. 认证授权节点演示');
    console.log('===================');

    try {
      // JWT令牌节点
      const jwtNode = NodeRegistry.createNode('JWTToken');
      if (jwtNode) {
        console.log('✅ JWT令牌节点创建成功');
        const tokenResult = jwtNode.execute({
          action: 'generate',
          payload: { userId: '123', role: 'admin' },
          secret: 'jwt-secret',
          expiresIn: '1h'
        });
        console.log('   令牌生成:', tokenResult.success ? '成功' : '失败');
      }

      // RBAC节点
      const rbacNode = NodeRegistry.createNode('RBAC');
      if (rbacNode) {
        console.log('✅ RBAC节点创建成功');
        const accessResult = rbacNode.execute({
          action: 'checkAccess',
          userId: '123',
          resource: 'project',
          operation: 'read'
        });
        console.log('   权限检查:', accessResult.success ? (accessResult.allowed ? '允许访问' : '拒绝访问') : '检查失败');
      }

      // 加密节点
      const encryptionNode = NodeRegistry.createNode('Encryption');
      if (encryptionNode) {
        console.log('✅ 加密节点创建成功');
        const encryptResult = encryptionNode.execute({
          data: 'sensitive data',
          algorithm: 'AES-256',
          key: 'encryption-key'
        });
        console.log('   数据加密:', encryptResult.success ? '成功' : '失败');
      }

    } catch (error) {
      console.error('❌ 认证授权节点演示失败:', error);
    }
    console.log();
  }

  /**
   * 演示通知服务节点
   */
  private async demonstrateNotificationNodes(): Promise<void> {
    console.log('📧 4. 通知服务节点演示');
    console.log('===================');

    try {
      // 邮件通知节点
      const emailNode = NodeRegistry.createNode('EmailNotification');
      if (emailNode) {
        console.log('✅ 邮件通知节点创建成功');
        const emailResult = emailNode.execute({
          to: '<EMAIL>',
          subject: '测试邮件',
          body: '这是一封测试邮件',
          template: 'default'
        });
        console.log('   邮件发送:', emailResult.success ? '成功' : '失败');
      }

      // 推送通知节点
      const pushNode = NodeRegistry.createNode('PushNotification');
      if (pushNode) {
        console.log('✅ 推送通知节点创建成功');
        const pushResult = pushNode.execute({
          deviceToken: 'device-token-123',
          title: '新消息',
          body: '您有一条新消息',
          data: { type: 'message' }
        });
        console.log('   推送发送:', pushResult.success ? '成功' : '失败');
      }

      // 通知调度节点
      const scheduleNode = NodeRegistry.createNode('NotificationSchedule');
      if (scheduleNode) {
        console.log('✅ 通知调度节点创建成功');
        const scheduleResult = scheduleNode.execute({
          action: 'schedule',
          notification: {
            type: 'email',
            recipient: '<EMAIL>',
            content: '定时通知'
          },
          scheduleTime: new Date(Date.now() + 3600000) // 1小时后
        });
        console.log('   通知调度:', scheduleResult.success ? '成功' : '失败');
      }

    } catch (error) {
      console.error('❌ 通知服务节点演示失败:', error);
    }
    console.log();
  }

  /**
   * 演示监控服务节点
   */
  private async demonstrateMonitoringNodes(): Promise<void> {
    console.log('📊 5. 监控服务节点演示');
    console.log('===================');

    try {
      // 系统监控节点
      const monitoringNode = NodeRegistry.createNode('SystemMonitoring');
      if (monitoringNode) {
        console.log('✅ 系统监控节点创建成功');
        const monitorResult = monitoringNode.execute({
          trigger: true,
          metrics: ['cpu', 'memory', 'disk'],
          interval: 60
        });
        console.log('   系统监控:', monitorResult.success ? `状态: ${monitorResult.status}` : '监控失败');
      }

      // 性能分析节点
      const performanceNode = NodeRegistry.createNode('PerformanceAnalysis');
      if (performanceNode) {
        console.log('✅ 性能分析节点创建成功');
        const analysisResult = performanceNode.execute({
          trigger: true,
          timeRange: '1h',
          metrics: ['response_time', 'throughput']
        });
        console.log('   性能分析:', analysisResult.success ? '分析完成' : '分析失败');
      }

      // 日志分析节点
      const logAnalysisNode = NodeRegistry.createNode('LogAnalysis');
      if (logAnalysisNode) {
        console.log('✅ 日志分析节点创建成功');
        const logResult = logAnalysisNode.execute({
          trigger: true,
          logSource: 'application',
          timeRange: '24h',
          filters: { level: 'error' }
        });
        console.log('   日志分析:', logResult.success ? `分析了${logResult.data?.summary?.totalLogs || 0}条日志` : '分析失败');
      }

    } catch (error) {
      console.error('❌ 监控服务节点演示失败:', error);
    }
    console.log();
  }

  /**
   * 演示项目管理节点
   */
  private async demonstrateProjectManagementNodes(): Promise<void> {
    console.log('📋 6. 项目管理节点演示');
    console.log('===================');

    try {
      // 创建项目节点
      const createProjectNode = NodeRegistry.createNode('CreateProject');
      if (createProjectNode) {
        console.log('✅ 创建项目节点创建成功');
        const createResult = createProjectNode.execute({
          name: '演示项目',
          description: '这是一个演示项目',
          template: 'default',
          author: 'demo-user'
        });
        console.log('   项目创建:', createResult.success ? `项目ID: ${createResult.project?.id}` : '创建失败');
      }

      // 项目版本节点
      const versionNode = NodeRegistry.createNode('ProjectVersion');
      if (versionNode) {
        console.log('✅ 项目版本节点创建成功');
        const versionResult = versionNode.execute({
          action: 'create',
          projectId: 'demo-project-123',
          version: '1.1.0',
          description: '新版本发布',
          changes: ['添加新功能', '修复bug']
        });
        console.log('   版本创建:', versionResult.success ? '成功' : '失败');
      }

      // 项目协作节点
      const collaborationNode = NodeRegistry.createNode('ProjectCollaboration');
      if (collaborationNode) {
        console.log('✅ 项目协作节点创建成功');
        const collabResult = collaborationNode.execute({
          action: 'invite',
          projectId: 'demo-project-123',
          userId: 'collaborator-456',
          role: 'editor'
        });
        console.log('   协作邀请:', collabResult.success ? '发送成功' : '发送失败');
      }

      // 项目备份节点
      const backupNode = NodeRegistry.createNode('ProjectBackup');
      if (backupNode) {
        console.log('✅ 项目备份节点创建成功');
        const backupResult = backupNode.execute({
          projectId: 'demo-project-123',
          type: 'full',
          destination: 'cloud-storage'
        });
        console.log('   项目备份:', backupResult.success ? '备份完成' : '备份失败');
      }

    } catch (error) {
      console.error('❌ 项目管理节点演示失败:', error);
    }
    console.log();
  }

  /**
   * 演示边缘设备节点
   */
  private async demonstrateEdgeDeviceNodes(): Promise<void> {
    console.log('🌐 7. 边缘设备节点演示');
    console.log('===================');

    try {
      // 边缘设备注册节点
      const registrationNode = NodeRegistry.createNode('EdgeDeviceRegistration');
      if (registrationNode) {
        console.log('✅ 边缘设备注册节点创建成功');
        const regResult = registrationNode.execute({
          deviceId: 'edge-device-001',
          deviceType: 'gateway',
          location: { lat: 39.9042, lng: 116.4074 },
          capabilities: ['compute', 'storage', 'network']
        });
        console.log('   设备注册:', regResult.success ? '注册成功' : '注册失败');
      }

      // 边缘设备监控节点
      const monitoringNode = NodeRegistry.createNode('EdgeDeviceMonitoring');
      if (monitoringNode) {
        console.log('✅ 边缘设备监控节点创建成功');
        const monitorResult = monitoringNode.execute({
          deviceId: 'edge-device-001',
          metrics: ['cpu', 'memory', 'network', 'temperature'],
          interval: 30
        });
        console.log('   设备监控:', monitorResult.success ? `状态: ${monitorResult.status}` : '监控失败');
      }

      // 边缘AI推理节点
      const aiInferenceNode = NodeRegistry.createNode('EdgeAIInference');
      if (aiInferenceNode) {
        console.log('✅ 边缘AI推理节点创建成功');
        const inferenceResult = aiInferenceNode.execute({
          modelId: 'image-classification-v1',
          input: { image: 'base64-encoded-image' },
          deviceId: 'edge-device-001'
        });
        console.log('   AI推理:', inferenceResult.success ? '推理完成' : '推理失败');
      }

      // 边缘模型部署节点
      const deploymentNode = NodeRegistry.createNode('EdgeModelDeployment');
      if (deploymentNode) {
        console.log('✅ 边缘模型部署节点创建成功');
        const deployResult = deploymentNode.execute({
          action: 'deploy',
          modelId: 'new-model-v2',
          deviceId: 'edge-device-001',
          config: { batch_size: 1, precision: 'fp16' }
        });
        console.log('   模型部署:', deployResult.success ? '部署成功' : '部署失败');
      }

    } catch (error) {
      console.error('❌ 边缘设备节点演示失败:', error);
    }
    console.log();
  }

  /**
   * 显示统计信息
   */
  private showStatistics(): void {
    console.log('📊 8. 统计信息');
    console.log('=============');

    const stats = this.registry.getNodeCategoryStats();
    console.log('节点分类统计:');
    console.log(`  📁 文件服务: ${stats.FILE_SERVICE} 个节点`);
    console.log(`  🔐 认证授权: ${stats.AUTHENTICATION} 个节点`);
    console.log(`  📧 通知服务: ${stats.NOTIFICATION} 个节点`);
    console.log(`  📊 监控服务: ${stats.MONITORING} 个节点`);
    console.log(`  📋 项目管理: ${stats.PROJECT_MANAGEMENT} 个节点`);
    console.log(`  🌐 边缘设备: ${stats.EDGE_COMPUTING} 个节点`);
    console.log(`  📈 总计: ${this.registry.getRegisteredNodeCount()} 个节点`);
    console.log();

    console.log('常量统计验证:');
    console.log(`  预期总数: ${SERVER_CLOUD_NODES_STATS.TOTAL_NODES}`);
    console.log(`  实际总数: ${this.registry.getRegisteredNodeCount()}`);
    console.log(`  统计匹配: ${SERVER_CLOUD_NODES_STATS.TOTAL_NODES === this.registry.getRegisteredNodeCount() ? '✅' : '❌'}`);
    console.log();
  }
}

// 运行演示的便捷函数
export async function runServerCloudNodesDemo(): Promise<void> {
  const demo = new ServerCloudNodesDemo();
  await demo.runFullDemo();
}

// 如果直接运行此文件，则执行演示
if (require.main === module) {
  runServerCloudNodesDemo().catch(console.error);
}
