/**
 * 注册批次6：服务器与云端节点注册表
 * 注册58个服务器与云端节点到编辑器
 * 包括文件服务（10个）、认证授权（7个）、通知服务（8个）、监控服务（5个）、项目管理（10个）、边缘设备管理（18个）
 */
import { NodeRegistry, NodeCategory, createNodeInfo } from './NodeRegistry';
import { Debug } from '../../utils/Debug';

// 导入文件服务节点（10个）
import {
  FileUploadNode,
  FileDownloadNode,
  FileStorageNode
} from '../nodes/file/FileServiceNodes';

import {
  FileCompressionNode,
  FileEncryptionNode,
  FileVersioningNode
} from '../nodes/file/FileServiceNodes2';

import {
  FileMetadataNode,
  FileSearchNode,
  FileSyncNode,
  FileAnalyticsNode
} from '../nodes/file/FileServiceNodes3';

// 导入认证授权节点（7个）
import {
  JWTTokenNode,
  OAuth2Node,
  RBACNode,
  PermissionCheckNode
} from '../nodes/auth/AuthenticationNodes';

import {
  SecurityAuditNode,
  EncryptionNode,
  DecryptionNode
} from '../nodes/auth/AuthenticationNodes2';

// 导入通知服务节点（8个）
import {
  EmailNotificationNode,
  PushNotificationNode,
  SMSNotificationNode
} from '../nodes/notification/NotificationServiceNodes';

import {
  InAppNotificationNode,
  NotificationTemplateNode
} from '../nodes/notification/NotificationServiceNodes2';

import {
  NotificationScheduleNode,
  NotificationAnalyticsNode,
  NotificationPreferenceNode
} from '../nodes/notification/NotificationServiceNodes3';

// 导入监控服务节点（5个）
import {
  SystemMonitoringNode,
  PerformanceAnalysisNode,
  AlertManagementNode,
  LogAnalysisNode,
  MetricsCollectionNode
} from '../nodes/monitoring/MonitoringServiceNodes';

// 导入项目管理节点（10个）
import {
  CreateProjectNode,
  LoadProjectNode,
  SaveProjectNode,
  ProjectVersionNode,
  ProjectCollaborationNode,
  ProjectPermissionNode,
  ProjectBackupNode,
  ProjectAnalyticsNode,
  ProjectTemplateNode,
  ProjectExportNode
} from '../nodes/project/ProjectManagementNodes';

// 导入边缘设备管理节点（18个）
import {
  EdgeDeviceRegistrationNode,
  EdgeDeviceMonitoringNode,
  EdgeDeviceControlNode
} from '../nodes/edge/EdgeDeviceNodes';

import {
  EdgeResourceManagementNode,
  EdgeNetworkNode
} from '../nodes/edge/EdgeDeviceNodes2';

import {
  EdgeSecurityNode,
  EdgeUpdateNode,
  EdgeDiagnosticsNode
} from '../nodes/edge/EdgeDeviceNodes3';

import {
  EdgePerformanceNode,
  EdgeFailoverNode
} from '../nodes/edge/EdgeDeviceNodes4';

import {
  EdgeConfigurationNode,
  EdgeMaintenanceNode,
  EdgeBackupNode
} from '../nodes/edge/EdgeDeviceNodes5';

import {
  EdgeSyncNode,
  EdgeAnalyticsNode
} from '../nodes/edge/EdgeDeviceNodes6';

import {
  EdgeAIInferenceNode,
  EdgeModelDeploymentNode,
  EdgeModelOptimizationNode
} from '../nodes/edge/EdgeAINodes';

/**
 * 服务器与云端节点注册表
 */
export class ServerCloudNodesRegistry {
  private static instance: ServerCloudNodesRegistry;
  private registered: boolean = false;
  private nodeRegistry: typeof NodeRegistry;

  constructor() {
    this.nodeRegistry = NodeRegistry;
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): ServerCloudNodesRegistry {
    if (!ServerCloudNodesRegistry.instance) {
      ServerCloudNodesRegistry.instance = new ServerCloudNodesRegistry();
    }
    return ServerCloudNodesRegistry.instance;
  }

  /**
   * 注册所有服务器与云端节点
   */
  public registerAll(): boolean {
    if (this.registered) {
      Debug.warn('ServerCloudNodesRegistry', '服务器与云端节点已经注册过了');
      return true;
    }

    try {
      Debug.log('ServerCloudNodesRegistry', '开始注册服务器与云端节点...');

      // 注册文件服务节点（10个）
      this.registerFileServiceNodes();
      
      // 注册认证授权节点（7个）
      this.registerAuthenticationNodes();
      
      // 注册通知服务节点（8个）
      this.registerNotificationNodes();
      
      // 注册监控服务节点（5个）
      this.registerMonitoringNodes();
      
      // 注册项目管理节点（10个）
      this.registerProjectManagementNodes();
      
      // 注册边缘设备管理节点（18个）
      this.registerEdgeDeviceNodes();

      this.registered = true;
      Debug.log('ServerCloudNodesRegistry', '服务器与云端节点注册完成！总计58个节点');
      
      return true;
    } catch (error) {
      Debug.error('ServerCloudNodesRegistry', '服务器与云端节点注册失败', error);
      return false;
    }
  }

  /**
   * 注册文件服务节点（10个）
   */
  private registerFileServiceNodes(): void {
    Debug.log('ServerCloudNodesRegistry', '注册文件服务节点...');

    const fileServiceNodes = [
      // 基础文件操作节点（3个）
      createNodeInfo(FileUploadNode, NodeCategory.FILE_SERVICE, '文件上传节点，支持多种文件格式上传'),
      createNodeInfo(FileDownloadNode, NodeCategory.FILE_SERVICE, '文件下载节点，支持断点续传和批量下载'),
      createNodeInfo(FileStorageNode, NodeCategory.FILE_SERVICE, '文件存储节点，支持多种存储后端'),

      // 文件处理节点（3个）
      createNodeInfo(FileCompressionNode, NodeCategory.FILE_SERVICE, '文件压缩节点，支持多种压缩格式'),
      createNodeInfo(FileEncryptionNode, NodeCategory.FILE_SERVICE, '文件加密节点，提供文件安全保护'),
      createNodeInfo(FileVersioningNode, NodeCategory.FILE_SERVICE, '文件版本控制节点，管理文件版本历史'),

      // 文件管理节点（4个）
      createNodeInfo(FileMetadataNode, NodeCategory.FILE_SERVICE, '文件元数据节点，管理文件属性信息'),
      createNodeInfo(FileSearchNode, NodeCategory.FILE_SERVICE, '文件搜索节点，提供全文搜索功能'),
      createNodeInfo(FileSyncNode, NodeCategory.FILE_SERVICE, '文件同步节点，实现多端文件同步'),
      createNodeInfo(FileAnalyticsNode, NodeCategory.FILE_SERVICE, '文件分析节点，提供文件使用统计')
    ];

    fileServiceNodes.forEach(nodeInfo => {
      this.nodeRegistry.registerNode(nodeInfo);
    });

    Debug.log('ServerCloudNodesRegistry', '文件服务节点注册完成：10个节点');
  }

  /**
   * 注册认证授权节点（7个）
   */
  private registerAuthenticationNodes(): void {
    Debug.log('ServerCloudNodesRegistry', '注册认证授权节点...');

    const authNodes = [
      // 认证节点（4个）
      createNodeInfo(JWTTokenNode, NodeCategory.AUTHENTICATION, 'JWT令牌节点，处理JWT令牌生成和验证'),
      createNodeInfo(OAuth2Node, NodeCategory.AUTHENTICATION, 'OAuth2节点，实现OAuth2认证流程'),
      createNodeInfo(RBACNode, NodeCategory.AUTHENTICATION, 'RBAC节点，基于角色的访问控制'),
      createNodeInfo(PermissionCheckNode, NodeCategory.AUTHENTICATION, '权限检查节点，验证用户权限'),

      // 安全节点（3个）
      createNodeInfo(SecurityAuditNode, NodeCategory.AUTHENTICATION, '安全审计节点，记录和分析安全事件'),
      createNodeInfo(EncryptionNode, NodeCategory.AUTHENTICATION, '加密节点，提供数据加密功能'),
      createNodeInfo(DecryptionNode, NodeCategory.AUTHENTICATION, '解密节点，提供数据解密功能')
    ];

    authNodes.forEach(nodeInfo => {
      this.nodeRegistry.registerNode(nodeInfo);
    });

    Debug.log('ServerCloudNodesRegistry', '认证授权节点注册完成：7个节点');
  }

  /**
   * 注册通知服务节点（8个）
   */
  private registerNotificationNodes(): void {
    Debug.log('ServerCloudNodesRegistry', '注册通知服务节点...');

    const notificationNodes = [
      // 基础通知节点（3个）
      createNodeInfo(EmailNotificationNode, NodeCategory.NOTIFICATION, '邮件通知节点，发送邮件通知'),
      createNodeInfo(PushNotificationNode, NodeCategory.NOTIFICATION, '推送通知节点，发送移动端推送'),
      createNodeInfo(SMSNotificationNode, NodeCategory.NOTIFICATION, '短信通知节点，发送短信通知'),

      // 高级通知节点（2个）
      createNodeInfo(InAppNotificationNode, NodeCategory.NOTIFICATION, '应用内通知节点，发送应用内消息'),
      createNodeInfo(NotificationTemplateNode, NodeCategory.NOTIFICATION, '通知模板节点，管理通知模板'),

      // 通知管理节点（3个）
      createNodeInfo(NotificationScheduleNode, NodeCategory.NOTIFICATION, '通知调度节点，管理定时通知'),
      createNodeInfo(NotificationAnalyticsNode, NodeCategory.NOTIFICATION, '通知分析节点，分析通知效果'),
      createNodeInfo(NotificationPreferenceNode, NodeCategory.NOTIFICATION, '通知偏好节点，管理用户通知偏好')
    ];

    notificationNodes.forEach(nodeInfo => {
      this.nodeRegistry.registerNode(nodeInfo);
    });

    Debug.log('ServerCloudNodesRegistry', '通知服务节点注册完成：8个节点');
  }

  /**
   * 注册监控服务节点（5个）
   */
  private registerMonitoringNodes(): void {
    Debug.log('ServerCloudNodesRegistry', '注册监控服务节点...');

    const monitoringNodes = [
      createNodeInfo(SystemMonitoringNode, NodeCategory.MONITORING, '系统监控节点，监控系统资源使用情况'),
      createNodeInfo(PerformanceAnalysisNode, NodeCategory.MONITORING, '性能分析节点，分析系统性能指标'),
      createNodeInfo(AlertManagementNode, NodeCategory.MONITORING, '告警管理节点，管理系统告警'),
      createNodeInfo(LogAnalysisNode, NodeCategory.MONITORING, '日志分析节点，分析系统日志'),
      createNodeInfo(MetricsCollectionNode, NodeCategory.MONITORING, '指标收集节点，收集系统指标数据')
    ];

    monitoringNodes.forEach(nodeInfo => {
      this.nodeRegistry.registerNode(nodeInfo);
    });

    Debug.log('ServerCloudNodesRegistry', '监控服务节点注册完成：5个节点');
  }

  /**
   * 注册项目管理节点（10个）
   */
  private registerProjectManagementNodes(): void {
    Debug.log('ServerCloudNodesRegistry', '注册项目管理节点...');

    const projectNodes = [
      // 基础项目操作节点（3个）
      createNodeInfo(CreateProjectNode, NodeCategory.PROJECT_MANAGEMENT, '创建项目节点，创建新的项目'),
      createNodeInfo(LoadProjectNode, NodeCategory.PROJECT_MANAGEMENT, '加载项目节点，加载现有项目'),
      createNodeInfo(SaveProjectNode, NodeCategory.PROJECT_MANAGEMENT, '保存项目节点，保存项目数据'),

      // 项目版本管理节点（1个）
      createNodeInfo(ProjectVersionNode, NodeCategory.PROJECT_MANAGEMENT, '项目版本节点，管理项目版本'),

      // 项目协作节点（2个）
      createNodeInfo(ProjectCollaborationNode, NodeCategory.PROJECT_MANAGEMENT, '项目协作节点，支持多人协作'),
      createNodeInfo(ProjectPermissionNode, NodeCategory.PROJECT_MANAGEMENT, '项目权限节点，管理项目权限'),

      // 项目维护节点（2个）
      createNodeInfo(ProjectBackupNode, NodeCategory.PROJECT_MANAGEMENT, '项目备份节点，备份项目数据'),
      createNodeInfo(ProjectAnalyticsNode, NodeCategory.PROJECT_MANAGEMENT, '项目分析节点，分析项目数据'),

      // 项目工具节点（2个）
      createNodeInfo(ProjectTemplateNode, NodeCategory.PROJECT_MANAGEMENT, '项目模板节点，管理项目模板'),
      createNodeInfo(ProjectExportNode, NodeCategory.PROJECT_MANAGEMENT, '项目导出节点，导出项目数据')
    ];

    projectNodes.forEach(nodeInfo => {
      this.nodeRegistry.registerNode(nodeInfo);
    });

    Debug.log('ServerCloudNodesRegistry', '项目管理节点注册完成：10个节点');
  }

  /**
   * 注册边缘设备管理节点（18个）
   */
  private registerEdgeDeviceNodes(): void {
    Debug.log('ServerCloudNodesRegistry', '注册边缘设备管理节点...');

    const edgeDeviceNodes = [
      // 基础设备管理节点（3个）
      createNodeInfo(EdgeDeviceRegistrationNode, NodeCategory.EDGE_COMPUTING, '边缘设备注册节点，注册边缘设备'),
      createNodeInfo(EdgeDeviceMonitoringNode, NodeCategory.EDGE_COMPUTING, '边缘设备监控节点，监控设备状态'),
      createNodeInfo(EdgeDeviceControlNode, NodeCategory.EDGE_COMPUTING, '边缘设备控制节点，控制设备操作'),

      // 设备资源管理节点（2个）
      createNodeInfo(EdgeResourceManagementNode, NodeCategory.EDGE_COMPUTING, '边缘资源管理节点，管理设备资源'),
      createNodeInfo(EdgeNetworkNode, NodeCategory.EDGE_COMPUTING, '边缘网络节点，管理设备网络连接'),

      // 设备安全与维护节点（3个）
      createNodeInfo(EdgeSecurityNode, NodeCategory.EDGE_COMPUTING, '边缘安全节点，保障设备安全'),
      createNodeInfo(EdgeUpdateNode, NodeCategory.EDGE_COMPUTING, '边缘更新节点，管理设备更新'),
      createNodeInfo(EdgeDiagnosticsNode, NodeCategory.EDGE_COMPUTING, '边缘诊断节点，诊断设备问题'),

      // 设备性能与故障处理节点（2个）
      createNodeInfo(EdgePerformanceNode, NodeCategory.EDGE_COMPUTING, '边缘性能节点，监控设备性能'),
      createNodeInfo(EdgeFailoverNode, NodeCategory.EDGE_COMPUTING, '边缘故障转移节点，处理设备故障'),

      // 设备配置与维护节点（3个）
      createNodeInfo(EdgeConfigurationNode, NodeCategory.EDGE_COMPUTING, '边缘配置节点，管理设备配置'),
      createNodeInfo(EdgeMaintenanceNode, NodeCategory.EDGE_COMPUTING, '边缘维护节点，维护设备状态'),
      createNodeInfo(EdgeBackupNode, NodeCategory.EDGE_COMPUTING, '边缘备份节点，备份设备数据'),

      // 设备同步与分析节点（2个）
      createNodeInfo(EdgeSyncNode, NodeCategory.EDGE_COMPUTING, '边缘同步节点，同步设备数据'),
      createNodeInfo(EdgeAnalyticsNode, NodeCategory.EDGE_COMPUTING, '边缘分析节点，分析设备数据'),

      // 边缘AI节点（3个）
      createNodeInfo(EdgeAIInferenceNode, NodeCategory.EDGE_COMPUTING, '边缘AI推理节点，执行AI推理'),
      createNodeInfo(EdgeModelDeploymentNode, NodeCategory.EDGE_COMPUTING, '边缘模型部署节点，部署AI模型'),
      createNodeInfo(EdgeModelOptimizationNode, NodeCategory.EDGE_COMPUTING, '边缘模型优化节点，优化AI模型')
    ];

    edgeDeviceNodes.forEach(nodeInfo => {
      this.nodeRegistry.registerNode(nodeInfo);
    });

    Debug.log('ServerCloudNodesRegistry', '边缘设备管理节点注册完成：18个节点');
  }

  /**
   * 获取注册状态
   */
  public isRegistered(): boolean {
    return this.registered;
  }

  /**
   * 获取注册的节点数量
   */
  public getRegisteredNodeCount(): number {
    return 58; // 10 + 7 + 8 + 5 + 10 + 18 = 58
  }

  /**
   * 获取节点分类统计
   */
  public getNodeCategoryStats(): Record<string, number> {
    return {
      'FILE_SERVICE': 10,
      'AUTHENTICATION': 7,
      'NOTIFICATION': 8,
      'MONITORING': 5,
      'PROJECT_MANAGEMENT': 10,
      'EDGE_COMPUTING': 18
    };
  }

  /**
   * 重置注册状态（用于测试）
   */
  public reset(): void {
    this.registered = false;
    Debug.log('ServerCloudNodesRegistry', '注册状态已重置');
  }
}

// 导出所有文件服务节点
export const FILE_SERVICE_NODES = [
  FileUploadNode,
  FileDownloadNode,
  FileStorageNode,
  FileCompressionNode,
  FileEncryptionNode,
  FileVersioningNode,
  FileMetadataNode,
  FileSearchNode,
  FileSyncNode,
  FileAnalyticsNode
];

// 导出所有认证授权节点
export const AUTHENTICATION_NODES = [
  JWTTokenNode,
  OAuth2Node,
  RBACNode,
  PermissionCheckNode,
  SecurityAuditNode,
  EncryptionNode,
  DecryptionNode
];

// 导出所有通知服务节点
export const NOTIFICATION_NODES = [
  EmailNotificationNode,
  PushNotificationNode,
  SMSNotificationNode,
  InAppNotificationNode,
  NotificationTemplateNode,
  NotificationScheduleNode,
  NotificationAnalyticsNode,
  NotificationPreferenceNode
];

// 导出所有监控服务节点
export const MONITORING_NODES = [
  SystemMonitoringNode,
  PerformanceAnalysisNode,
  AlertManagementNode,
  LogAnalysisNode,
  MetricsCollectionNode
];

// 导出所有项目管理节点
export const PROJECT_MANAGEMENT_NODES = [
  CreateProjectNode,
  LoadProjectNode,
  SaveProjectNode,
  ProjectVersionNode,
  ProjectCollaborationNode,
  ProjectPermissionNode,
  ProjectBackupNode,
  ProjectAnalyticsNode,
  ProjectTemplateNode,
  ProjectExportNode
];

// 导出所有边缘设备管理节点
export const EDGE_DEVICE_NODES = [
  EdgeDeviceRegistrationNode,
  EdgeDeviceMonitoringNode,
  EdgeDeviceControlNode,
  EdgeResourceManagementNode,
  EdgeNetworkNode,
  EdgeSecurityNode,
  EdgeUpdateNode,
  EdgeDiagnosticsNode,
  EdgePerformanceNode,
  EdgeFailoverNode,
  EdgeConfigurationNode,
  EdgeMaintenanceNode,
  EdgeBackupNode,
  EdgeSyncNode,
  EdgeAnalyticsNode,
  EdgeAIInferenceNode,
  EdgeModelDeploymentNode,
  EdgeModelOptimizationNode
];

// 导出所有服务器与云端节点
export const SERVER_CLOUD_NODES = [
  ...FILE_SERVICE_NODES,
  ...AUTHENTICATION_NODES,
  ...NOTIFICATION_NODES,
  ...MONITORING_NODES,
  ...PROJECT_MANAGEMENT_NODES,
  ...EDGE_DEVICE_NODES
];

// 节点统计常量
export const SERVER_CLOUD_NODES_STATS = {
  TOTAL_NODES: 58,
  FILE_SERVICE_COUNT: 10,
  AUTHENTICATION_COUNT: 7,
  NOTIFICATION_COUNT: 8,
  MONITORING_COUNT: 5,
  PROJECT_MANAGEMENT_COUNT: 10,
  EDGE_DEVICE_COUNT: 18
};

// 默认导出注册表实例
export default ServerCloudNodesRegistry.getInstance();
