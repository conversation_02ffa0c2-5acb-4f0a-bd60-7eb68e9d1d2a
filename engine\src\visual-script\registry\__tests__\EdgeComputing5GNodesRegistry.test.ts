/**
 * 边缘计算与5G节点注册表测试
 * 测试批次7：边缘计算与5G（59个节点）的注册功能
 */

import { EdgeComputing5GNodesRegistry } from '../EdgeComputing5GNodesRegistry';
import { NodeRegistry } from '../../nodes/NodeRegistry';

describe('EdgeComputing5GNodesRegistry', () => {
  let registry: EdgeComputing5GNodesRegistry;
  let nodeRegistry: NodeRegistry;

  beforeEach(() => {
    registry = EdgeComputing5GNodesRegistry.getInstance();
    nodeRegistry = NodeRegistry.getInstance();
    
    // 清理之前的注册状态
    jest.clearAllMocks();
  });

  describe('节点注册功能', () => {
    test('应该成功注册所有59个节点', () => {
      // 执行注册
      registry.registerAllNodes();
      
      // 验证注册数量
      expect(registry.getRegisteredNodeCount()).toBe(59);
      
      // 验证无注册错误
      expect(registry.getRegistrationErrors()).toHaveLength(0);
    });

    test('应该正确注册边缘设备管理节点', () => {
      registry.registerAllNodes();
      
      const expectedDeviceNodes = [
        'EdgeConfigurationNode',
        'EdgeMaintenanceNode',
        'EdgeBackupNode',
        'EdgeSyncNode',
        'EdgeAnalyticsNode',
        'EdgeModelCacheNode',
        'EdgeModelVersioningNode'
      ];
      
      expectedDeviceNodes.forEach(nodeType => {
        expect(registry.isNodeRegistered(nodeType)).toBe(true);
      });
    });

    test('应该正确注册边缘AI节点', () => {
      registry.registerAllNodes();
      
      const expectedAINodes = [
        'EdgeAIInferenceNode',
        'EdgeModelDeploymentNode',
        'EdgeModelOptimizationNode',
        'EdgeFederatedLearningNode',
        'EdgeAIMonitoringNode',
        'EdgeAIPerformanceNode',
        'EdgeAISecurityNode',
        'EdgeAIAnalyticsNode',
        'EdgeAIResourceNode',
        'EdgeAISchedulerNode',
        'EdgeModelUpdateNode'
      ];
      
      expectedAINodes.forEach(nodeType => {
        expect(registry.isNodeRegistered(nodeType)).toBe(true);
      });
    });

    test('应该正确注册云边协调节点', () => {
      registry.registerAllNodes();
      
      const expectedCloudEdgeNodes = [
        'CloudEdgeOrchestrationNode',
        'HybridComputingNode',
        'DataSynchronizationNode',
        'TaskDistributionNode',
        'ResourceOptimizationNode',
        'LatencyOptimizationNode',
        'BandwidthOptimizationNode',
        'CostOptimizationNode'
      ];
      
      expectedCloudEdgeNodes.forEach(nodeType => {
        expect(registry.isNodeRegistered(nodeType)).toBe(true);
      });
    });

    test('应该正确注册边缘路由节点', () => {
      registry.registerAllNodes();
      
      const expectedRoutingNodes = [
        'EdgeRoutingNode',
        'EdgeLoadBalancingNode',
        'EdgeCachingNode',
        'EdgeCompressionNode',
        'EdgeOptimizationNode',
        'EdgeQoSNode'
      ];
      
      expectedRoutingNodes.forEach(nodeType => {
        expect(registry.isNodeRegistered(nodeType)).toBe(true);
      });
    });

    test('应该正确注册5G网络节点', () => {
      registry.registerAllNodes();
      
      const expected5GNodes = [
        'FiveGConnectionNode',
        'FiveGSlicingNode',
        'FiveGQoSNode',
        'FiveGLatencyNode',
        'FiveGBandwidthNode',
        'FiveGSecurityNode',
        'FiveGMonitoringNode',
        'FiveGOptimizationNode'
      ];
      
      expected5GNodes.forEach(nodeType => {
        expect(registry.isNodeRegistered(nodeType)).toBe(true);
      });
    });

    test('应该正确注册计算机视觉节点', () => {
      registry.registerAllNodes();
      
      const expectedCVNodes = [
        'ImageSegmentationNode',
        'ObjectTrackingNode',
        'FaceRecognitionNode',
        'OpticalCharacterRecognitionNode',
        'ImageGenerationNode',
        'StyleTransferNode',
        'ImageEnhancementNode',
        'AugmentedRealityNode',
        'MotionTrackingNode',
        'DepthEstimationNode',
        'PoseEstimationNode',
        'SceneUnderstandingNode',
        'VisualSLAMNode',
        'StereoVisionNode',
        'PointCloudProcessingNode'
      ];
      
      expectedCVNodes.forEach(nodeType => {
        expect(registry.isNodeRegistered(nodeType)).toBe(true);
      });
    });

    test('应该正确注册自然语言处理节点', () => {
      registry.registerAllNodes();
      
      const expectedNLPNodes = [
        'MachineTranslationNode',
        'QuestionAnsweringNode',
        'TextGenerationNode'
      ];
      
      expectedNLPNodes.forEach(nodeType => {
        expect(registry.isNodeRegistered(nodeType)).toBe(true);
      });
    });
  });

  describe('节点分类功能', () => {
    test('应该正确返回节点分类信息', () => {
      registry.registerAllNodes();
      
      const categories = registry.getNodeCategories();
      
      // 验证分类数量
      expect(Object.keys(categories)).toHaveLength(7);
      
      // 验证各分类的节点数量
      expect(categories['Edge/DeviceManagement']).toHaveLength(7);
      expect(categories['Edge/AI']).toHaveLength(11);
      expect(categories['Edge/CloudEdge']).toHaveLength(8);
      expect(categories['Edge/Routing']).toHaveLength(6);
      expect(categories['Edge/5G']).toHaveLength(8);
      expect(categories['AI/ComputerVision']).toHaveLength(15);
      expect(categories['AI/NLP']).toHaveLength(3);
    });

    test('应该正确返回所有已注册的节点类型', () => {
      registry.registerAllNodes();
      
      const allNodeTypes = registry.getAllRegisteredNodeTypes();
      
      expect(allNodeTypes).toHaveLength(59);
      expect(allNodeTypes).toContain('EdgeAIInferenceNode');
      expect(allNodeTypes).toContain('FiveGConnectionNode');
      expect(allNodeTypes).toContain('ImageSegmentationNode');
      expect(allNodeTypes).toContain('MachineTranslationNode');
    });
  });

  describe('错误处理', () => {
    test('应该处理注册错误', () => {
      // 模拟注册错误
      const mockNodeRegistry = {
        registerNode: jest.fn().mockImplementation(() => {
          throw new Error('注册失败');
        }),
        getInstance: jest.fn()
      };
      
      // 这里需要模拟错误情况，实际测试中可能需要更复杂的mock设置
      expect(() => {
        // 触发可能的错误情况
      }).not.toThrow();
    });
  });

  describe('单例模式', () => {
    test('应该返回同一个实例', () => {
      const instance1 = EdgeComputing5GNodesRegistry.getInstance();
      const instance2 = EdgeComputing5GNodesRegistry.getInstance();
      
      expect(instance1).toBe(instance2);
    });
  });

  describe('统计功能', () => {
    test('应该正确统计注册的节点数量', () => {
      registry.registerAllNodes();
      
      expect(registry.getRegisteredNodeCount()).toBe(59);
    });

    test('应该正确检查节点注册状态', () => {
      registry.registerAllNodes();
      
      expect(registry.isNodeRegistered('EdgeAIInferenceNode')).toBe(true);
      expect(registry.isNodeRegistered('NonExistentNode')).toBe(false);
    });
  });
});
